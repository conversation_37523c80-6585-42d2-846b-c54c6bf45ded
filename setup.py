from __future__ import annotations

from datetime import datetime
from json import load
from logging import DEBUG, basicConfig, debug
from pathlib import Path
from platform import system
from re import DOTALL, IGNORECASE, MULTILINE, escape, finditer, search, sub
from shutil import copyfile
from site import getsitepackages, getusersitepackages
from typing import Any

from setuptools import setup
from setuptools.command.develop import develop
from setuptools.command.install import install


def backup_file(file_path: Path) -> Path:
    """Create a timestamped backup of the given file."""
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    backup_path = file_path.with_suffix(f".{timestamp}.bak")
    copyfile(str(file_path), str(backup_path))
    return backup_path


def load_json(path: Path) -> dict[str, Any]:
    with path.open(encoding="utf-8") as f:
        return load(f)


def find_existing_items(
    shelf_block: str,
    required_items: list[dict[str, Any]],
) -> list[str]:
    existing = []
    for match in finditer(
        r"\s{12}shelf_item {(.*?)(?<! ) {12}(?! )}",
        shelf_block,
        MULTILINE | DOTALL,
    ):
        attrs = search(
            r"\s{16}title \"(?P<title>.*?)\".*"
            r"\s{16}description \"(?P<description>.*?)\".*"
            r"\s{16}script_filename \"(?P<script_filename>.*?)\".*"
            r"\s{16}icon_filename \"(?P<icon_filename>.*?)\".*",
            shelf_block[match.start() : match.end()],
            MULTILINE | DOTALL,
        )
        if attrs:
            for req in required_items:
                if req.get("title") == attrs.group("title"):
                    existing.append(attrs.group("title"))
    return existing


def resolve_file_in_sitepackages(filename: str, package_name: str) -> str:
    for folder in getsitepackages() + [getusersitepackages()]:
        candidate = Path(folder) / package_name / filename
        if candidate.is_file():
            return str(candidate).replace("\\", "/")
    return ""


def generate_shelf_item_block(item: dict[str, Any], package_name: str) -> str:
    script_filename = resolve_file_in_sitepackages(
        item.get("script_filename", ""),
        package_name,
    )
    icon_filename = resolve_file_in_sitepackages(
        item.get("icon_filename", ""),
        package_name,
    )
    block = [
        "            shelf_item {",
        f'                title "{item.get("title", "")}"',
    ]
    if item.get("description"):
        block.append(f'                description "{item["description"]}"')
    block.append(f'                script_filename "{script_filename}"')
    if item.get("icon_filename"):
        block.append(f'                icon_filename "{icon_filename}"')
    block.append("            }")
    return "\n".join(block)


def update_shelf_config(
    shelf_file: str,
    shelf_title: str,
    required_items: list[dict[str, Any]],
    slot: int,
    package_name: str,
) -> str:
    view_mode_search = search(r"view_mode [0-9]", shelf_file)
    slot_search = search(r"slot [0-9] {", shelf_file)
    category_search = None
    write_index = 0
    generated = ""
    if view_mode_search:
        write_index = view_mode_search.end()
    if slot_search:
        write_index = slot_search.end() + 1
        category_search = search(
            rf'category "{escape(shelf_title)}" {{(.*?)(?<! ) {{8}}(?! )}}',
            shelf_file,
            MULTILINE | DOTALL | IGNORECASE,
        )
    else:
        generated += f"\n    slot {slot} {{\n"
    existing_items = []
    if category_search:
        write_index = category_search.end()
        block = shelf_file[category_search.start() : category_search.end()]
        existing_items = find_existing_items(block, required_items)
    else:
        generated += f'        category "{shelf_title}" {{\n'
    if len(existing_items) < len(required_items):
        generated += "\n"
    for item in required_items:
        if item.get("title") not in existing_items:
            generated += generate_shelf_item_block(item, package_name) + "\n"
    if not category_search:
        generated += "        }\n"
    if not slot_search:
        generated += "    }\n"
    if not view_mode_search:
        generated = "\n" + generated
    new_cfg = shelf_file[:write_index] + generated + shelf_file[write_index:]
    new_cfg = new_cfg.replace("\t", "    ")
    cleaned = "\n".join(line for line in new_cfg.split("\n") if line.strip())
    return sub(
        r'category_selected "[\w\s]+"',
        f'category_selected "{shelf_title}"',
        cleaned,
    )


def setup_shelf(shelf_path: Path, slot: int = 0) -> None:
    """Set up the shelf configuration for Clarisse."""
    log_path = shelf_path.parent / "shelf_installation.log"
    basicConfig(
        filename=str(log_path),
        level=DEBUG,
        format="%(message)s",
    )
    log_start = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    debug("--------------------------------------")
    debug(f"Log start: {log_start}")
    backup_file(shelf_path)
    with shelf_path.open(encoding="utf-8") as f:
        shelf_file = f.read()
    json_data = load_json(Path(script_dir) / package_name / "shelf.json")
    shelf_title = json_data.get("category", "")
    required_items = json_data.get("shelf_items", [])
    new_cfg = update_shelf_config(
        shelf_file,
        shelf_title,
        required_items,
        slot,
        package_name,
    )
    with shelf_path.open("w", encoding="utf-8") as f:
        f.write(new_cfg)
        debug("...Shelf installed!")
    log_end = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    debug(f"Log end: {log_end}")


class PostDevelopCommand(develop):
    """Post-installation for development mode."""

    def run(self) -> None:
        super().run()
        print("Post develop command running...")


class PostInstallCommand(install):
    """Post-installation for installation mode."""

    def run(self) -> None:
        super().run()
        print("Post installation command running...")
        clarisse_dir = self._get_clarisse_dir()
        if clarisse_dir is not None and clarisse_dir.exists():
            self._install_shelf_for_versions(clarisse_dir)

    @staticmethod
    def _get_clarisse_dir() -> Path | None:
        sys = system().lower()
        if sys == "windows":
            appdata = (
                Path.home().drive + "/" + Path.home().parts[1] + "/AppData/Roaming"
            )
            appdata_path = Path(appdata)
            if not appdata_path.exists():
                return None
            return appdata_path / "Isotropix" / "Clarisse"
        if sys.startswith("linux"):
            return Path.home() / ".isotropix" / "clarisse"
        if sys == "darwin":
            return Path.home() / "Library" / "Preferences" / "Isotropix" / "Clarisse"
        return None

    def _install_shelf_for_versions(self, clarisse_dir: Path) -> None:
        sys = system().lower()
        for version_dir in clarisse_dir.iterdir():
            if not version_dir.is_dir():
                continue
            version = version_dir.name
            if sys == "darwin":
                match = search(r"(\d[.\d]*)$", version)
                if not (match and match.group(1) in versions):
                    continue
            elif version not in versions:
                continue
            shelf_path = version_dir / "shelf.cfg"
            if shelf_path.is_file():
                setup_shelf(shelf_path)
                if sys == "darwin":
                    print(f"sp: {shelf_path}")


script_dir = str(Path(__file__).resolve().parent)
package_name = "clarisse_survival_kit"
versions = ["5.0"]

# For modern packaging, we rely on pyproject.toml for most metadata
# but keep setup.py for custom installation commands
setup(
    cmdclass={
        "develop": PostDevelopCommand,
        "install": PostInstallCommand,
    },
)
