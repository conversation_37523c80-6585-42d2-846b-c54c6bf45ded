"""Clarisse Survival Kit package initialization.

This module sets up the package environment, user settings, and logging configuration
for the Clarisse Survival Kit. It handles cross-platform user directory detection
and package path configuration.
"""

from __future__ import annotations

import sys
from contextlib import suppress
from datetime import datetime
from logging import ERROR, basicConfig, debug
from os import environ, getenv
from pathlib import Path
from platform import system as platform_system
from site import getsitepackages, getusersitepackages
from sys import path

# Prevent Python from writing .pyc bytecode files
sys.dont_write_bytecode = True

# Constants
LOGGING_FILENAME: str = "clarisse_survival_kit.log"
SETTINGS_FILENAME: str = "user_settings.py"
CSK_DIRECTORY_NAME: str = ".csk"
PACKAGE_NAME: str = "clarisse_survival_kit"


def get_isotropix_user_path() -> Path:
    """Get the Isotropix user directory path based on the operating system.

    Returns:
        Path to the Isotropix user directory, or empty Path if unsupported OS

    """
    system_name = platform_system().lower()

    if system_name == "windows":
        appdata = getenv("APPDATA")
        if not appdata:
            return Path()
        return Path(appdata) / "Isotropix"

    if system_name.startswith("linux"):
        return Path.home() / ".isotropix"

    if system_name == "darwin":
        return Path.home() / "Library" / "Preferences" / "Isotropix"

    return Path()


def create_init_file(init_path: Path) -> None:
    """Create an empty __init__.py file if it doesn't exist.

    Args:
        init_path: Path to the __init__.py file to create

    """
    if not init_path.is_file():
        init_path.touch()


def create_settings_file(settings_path: Path) -> None:
    """Create an empty user settings file if it doesn't exist.

    Args:
        settings_path: Path to the settings file to create

    """
    if not settings_path.is_file():
        settings_path.touch()


def load_user_log_level() -> int:
    """Load the log level from user settings.

    Returns:
        Log level from user settings, or ERROR as default

    """
    try:
        from user_settings import LOG_LEVEL  # type: ignore  # noqa: PLC0415
    except ImportError:
        return ERROR
    else:
        return LOG_LEVEL


def clear_log_file(log_path: Path) -> None:
    """Clear the log file if it exists.

    Args:
        log_path: Path to the log file to clear

    """
    if log_path.is_file():
        log_path.write_text("")


def load_package_path(settings_path: Path) -> str:
    """Load package path from user settings or discover it automatically.

    Args:
        settings_path: Path to the settings file

    Returns:
        Package path if found, empty string otherwise

    """
    try:
        from user_settings import PACKAGE_PATH  # type: ignore  # noqa: PLC0415
    except ImportError:
        return _discover_path(settings_path)
    else:
        return PACKAGE_PATH


def _discover_path(settings_path: Path) -> str:
    """Discover the package path by searching site-packages directories.

    Args:
        settings_path: Path to the settings file to update

    Returns:
        Discovered package path if found, empty string otherwise

    """
    sitepackages_folders = getsitepackages() + [getusersitepackages()]

    for sitepackages_folder in sitepackages_folders:
        sitepackages_path = Path(sitepackages_folder)
        if not sitepackages_path.is_dir():
            continue

        for sub_folder in sitepackages_path.iterdir():
            if sub_folder.name == PACKAGE_NAME and sub_folder.is_dir():
                package_path = str(sub_folder)
                _save_path_to_settings(settings_path, package_path)
                return package_path

    return ""


def _save_path_to_settings(settings_path: Path, package_path: str) -> None:
    """Save the discovered package path to the settings file.

    Args:
        settings_path: Path to the settings file
        package_path: Package path to save

    """
    with settings_path.open("a", encoding="utf-8") as settings_file:
        settings_file.write(f'\nPACKAGE_PATH = r"{package_path}"')


def setup_logging(log_path: Path, log_level: int) -> None:
    """Set up logging configuration.

    Args:
        log_path: Path to the log file
        log_level: Logging level to use

    """
    basicConfig(filename=log_path, level=log_level, format="%(message)s")
    log_start = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    debug("--------------------------------------")
    debug(f"Log start: {log_start}")


def initialize_package() -> None:
    """Initialize the package environment and configuration."""
    user_path = get_isotropix_user_path() / CSK_DIRECTORY_NAME

    if not user_path:
        print("Could not generate log or user settings!!!")
        return

    # Add user path to Python path
    path.append(str(user_path))

    # Create user directory if it doesn't exist
    if not user_path.exists():
        user_path.mkdir(parents=True, exist_ok=True)

    # Create necessary files
    create_init_file(user_path / "__init__.py")

    settings_path = user_path / SETTINGS_FILENAME
    create_settings_file(settings_path)

    # Load configuration
    log_level = load_user_log_level()

    # Set up logging
    log_path = user_path / LOGGING_FILENAME
    clear_log_file(log_path)
    setup_logging(log_path, log_level)

    # Set up package path
    package_path = load_package_path(settings_path)
    if package_path:
        environ["CSK_PACKAGE_PATH"] = package_path


def main() -> None:
    """Entry point."""

    # Initialize the package when module is imported
    initialize_package()


if __name__ == "__main__":
    main()
