{"category": "Clarisse Survival Kit", "shelf_items": [{"title": "Import Asset", "description": "Imports a Megascans asset or generic texture item as a new context. Geometry gets imported also and materials are automatically assigned.", "script_filename": "import_asset.py", "icon_filename": "import_asset.png"}, {"title": "Mix Surfaces", "description": "Mixes multiple selected surfaces with a cover surface. You can quickly cover multiple objects with dirt or snow.", "script_filename": "mix.py", "icon_filename": "mix.png"}, {"title": "Add Surface(s) to Mix", "description": "Adds the selected surfaces to an existing mix.", "script_filename": "add_to_mix.py", "icon_filename": "add_to_mix.png"}, {"title": "Textures to Triplanar", "description": "Converts the selected texture(s) to triplanar.", "script_filename": "triplanar.py", "icon_filename": "triplanar.png"}, {"title": "Replace Surface", "description": "Replaces the selected surfaces and updates the mapping.", "script_filename": "replace.py", "icon_filename": "replace.png"}, {"title": "Toggle Surface Complexity", "description": "Temporarily converts the selected surface to a simple PhysicalDiffuse material and disables the Displacement map. This can be handy when mixing two materials to reduce overhead.", "script_filename": "simplify.py", "icon_filename": "simplify.png"}, {"title": "Generate Decimated Point Cloud", "description": "Quickly generate a decimated point cloud from the selected object with several selectors for masking.", "script_filename": "scatter.py", "icon_filename": "scatter.png"}, {"title": "<PERSON><PERSON><PERSON>", "description": "Adds a wet layer on top of the selected surface. Requires Diffuse, Specular and Roughness texture map files to be set in your material.", "script_filename": "moisten.py", "icon_filename": "moisten.png"}, {"title": "Quick Blend", "description": "Creates a blend or multiblend mix when two or more Textures(any type), Normal Maps, Displacement Maps are selected or a MaterialPhysicalBlend or MaterialPhysicalMultiBlend node if two or more Physical materials are selected.", "script_filename": "blend.py", "icon_filename": "blend.png"}, {"title": "Mask Blend <PERSON>des", "description": "Adds selectors to the selected blend nodes.", "script_filename": "mask.py", "icon_filename": "mask.png"}, {"title": "Tint Surface", "description": "Tints the diffuse with a custom color. Handy when you blend two materials together and when they look different.", "script_filename": "tint.py", "icon_filename": "tint.png"}, {"title": "Blur Textures", "description": "Blurs the selected texture.", "script_filename": "blur.py", "icon_filename": "blur.png"}, {"title": "(Un)Stream Textures", "description": "Converts selected regular map files to TextureStreamedMapFile and vice versa.", "script_filename": "stream_toggle.py", "icon_filename": "stream_toggle.png"}, {"title": "Convert Textures", "description": "Converts selected textures to another file type.", "script_filename": "converter.py", "icon_filename": "converter.png"}, {"title": "Reconvert Selected Textures", "description": "Scans each of the selected textures folder for updated source files and reconverts them if needed.", "script_filename": "reconvert.py", "icon_filename": "reconvert.png"}, {"title": "Import Megascans Library", "description": "Imports the whole or selected categories of the Megascans Library. Already existing items will be ignored.", "script_filename": "import_ms_library.py", "icon_filename": "import_ms_library.png"}, {"title": "Megascans Bridge", "description": "Starts the Megascans Bridge listener for incoming requests. Make sure Command Port is enabled.", "script_filename": "ms_bridge_gui.py", "icon_filename": "ms_bridge_gui.png"}, {"title": "Setup Heightmap", "description": "Imports a heightmap and displaces it on a polygrid. Can also generate a low resolution proxy.", "script_filename": "terrain.py", "icon_filename": "terrain.png"}, {"title": "Toggle all map color space to ACES or Clarisse", "description": "After you change your Color Management configuration via preferences this script allows you to fix the color spaces of all textures.", "script_filename": "aces.py", "icon_filename": "aces.png"}]}