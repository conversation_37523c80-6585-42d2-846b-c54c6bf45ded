# Installation Guide for clarisse_survival_kit

## 🚨 Important: Stop Using `python setup.py install`

The `python setup.py install` command is **deprecated** and should no longer be used. This guide shows you the modern, recommended approaches using **pip** and **uv**.

## Why `python setup.py install` is Deprecated

### Problems with the old method

- ❌ **No dependency resolution** - Can't handle version conflicts
- ❌ **No uninstall capability** - Can't remove packages cleanly
- ❌ **No isolation** - Directly modifies Python environment
- ❌ **Security risks** - Runs arbitrary code without sandboxing
- ❌ **No caching** - Rebuilds everything from scratch
- ❌ **Poor metadata** - Doesn't register properly with package managers
- ❌ **Environment pollution** - Can break other packages

### Modern benefits

- ✅ **Proper dependency management** with pip/uv
- ✅ **Easy uninstallation** with `pip uninstall` or `uv pip uninstall`
- ✅ **Virtual environment support**
- ✅ **Metadata tracking** for better package management
- ✅ **Security and isolation**
- ✅ **Reproducible environments**
- ✅ **Fast installation** (especially with uv)

## 🚀 Package Manager Options

### pip (Standard Python Package Manager)

- ✅ **Widely supported** - Default Python package manager
- ✅ **Stable and reliable** - Battle-tested
- ✅ **Universal compatibility** - Works everywhere

### uv (Modern Fast Alternative)

- ⚡ **10-100x faster** - Built in Rust for speed
- ✅ **pip-compatible** - Drop-in replacement
- ✅ **Better caching** - More efficient dependency resolution
- ✅ **Modern tooling** - Active development

## 🎯 Recommended Installation Methods

### 1. Development Installation (Recommended for Development)

**With pip:**

```bash
# Install in editable mode - changes to code are immediately reflected
pip install -e .
```

**With uv (faster):**

```bash
# Same functionality, much faster
uv pip install -e .
```

**Benefits:**

- Code changes are immediately available without reinstalling
- Runs your custom `PostDevelopCommand`
- Perfect for development and testing

### 2. Production Installation

**With pip:**

```bash
# Regular installation for production use
pip install .
```

**With uv (faster):**

```bash
# Same functionality, much faster
uv pip install .
```

**Benefits:**

- Installs the package normally
- Runs your custom `PostInstallCommand`
- Sets up Clarisse shelf integration automatically

### 3. Install from Git Repository

**With pip:**

```bash
# Install directly from GitHub
pip install git+https://github.com/aydinyanik/clarisse_survival_kit.git

# Install specific branch or tag
pip install git+https://github.com/aydinyanik/clarisse_survival_kit.git@main
```

**With uv:**

```bash
# Install directly from GitHub (faster)
uv pip install git+https://github.com/aydinyanik/clarisse_survival_kit.git
```

### 4. Build and Distribute

**With pip:**

```bash
# Install build tools
pip install build

# Build the package
python -m build

# Install from built wheel
pip install dist/clarisse_survival_kit-2.0.0-py3-none-any.whl
```

**With uv:**

```bash
# Install build tools (faster)
uv pip install build

# Build the package
python -m build

# Install from built wheel (faster)
uv pip install dist/clarisse_survival_kit-2.0.0-py3-none-any.whl
```

## 🛠️ Setting Up uv

### Install uv

**Option 1: Official installer (recommended):**

```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

**Option 2: Via pip:**

```bash
pip install uv
```

**Option 3: Via homebrew (macOS):**

```bash
brew install uv
```

### Verify uv installation

```bash
uv --version
```

## 🔧 Package Management Commands

### Installation Commands

**With pip:**

```bash
# Development install (editable)
pip install -e .

# Production install
pip install .

# Install with verbose output
pip install -v .
```

**With uv (faster alternatives):**

```bash
# Development install (editable)
uv pip install -e .

# Production install
uv pip install .

# Install with verbose output
uv pip install -v .
```

### Uninstallation

**With pip:**

```bash
# Uninstall the package
pip uninstall clarisse-survival-kit
```

**With uv:**

```bash
# Uninstall the package
uv pip uninstall clarisse-survival-kit
```

### Upgrade

**With pip:**

```bash
# Upgrade to latest version
pip install --upgrade .

# Force reinstall
pip install --force-reinstall .
```

**With uv:**

```bash
# Upgrade to latest version
uv pip install --upgrade .

# Force reinstall
uv pip install --reinstall .
```

### Check Installation

**With pip:**

```bash
# List installed packages
pip list | grep clarisse

# Show package information
pip show clarisse-survival-kit
```

**With uv:**

```bash
# List installed packages
uv pip list | grep clarisse

# Show package information
uv pip show clarisse-survival-kit
```

## 🚀 Quick Start Script

Use the provided `interactive_install.py` script for guided installation:

```bash
python3 interactive_install.py
```

This script provides interactive options for:

1. Development installation (pip)
2. Production installation (pip)
3. Building for distribution
4. Installing from built wheel
5. Production installation with uv (if available)
6. Development installation with uv (if available)

The script automatically detects if uv is available and shows additional fast installation options.

## 🔄 Migration from Old Method

### Before (Deprecated)

```bash
python setup.py install  # ❌ Don't use this anymore
```

### After (Modern)

```bash
# Choose one based on your preference:
pip install .      # ✅ Standard approach
uv pip install .   # ✅ Faster approach
```

## 🐍 Virtual Environment Best Practices

### With pip and venv

```bash
# Create virtual environment
python -m venv clarisse_env

# Activate (Linux/Mac)
source clarisse_env/bin/activate

# Activate (Windows)
clarisse_env\Scripts\activate

# Install in virtual environment
pip install -e .

# Deactivate when done
deactivate
```

### With uv (recommended)

```bash
# Create and manage virtual environment with uv
uv venv clarisse_env

# Activate (Linux/Mac)
source clarisse_env/bin/activate

# Install in virtual environment (faster)
uv pip install -e .

# Deactivate when done
deactivate
```

## 🎯 Specific Commands for Your Package

### For Development

```bash
# Standard approach
pip install -e .

# Faster approach
uv pip install -e .
```

This will:

- Install in editable mode
- Run `PostDevelopCommand`
- Allow immediate code changes

### For Production

```bash
# Standard approach
pip install .

# Faster approach
uv pip install .
```

This will:

- Install normally
- Run `PostInstallCommand`
- Set up Clarisse shelf integration

### For Distribution

```bash
# Build package
python -m build

# Install from wheel (standard)
pip install dist/clarisse_survival_kit-2.0.0-py3-none-any.whl

# Install from wheel (faster)
uv pip install dist/clarisse_survival_kit-2.0.0-py3-none-any.whl
```

## 🔍 Troubleshooting

### If you get permission errors

```bash
# Install for current user only
pip install --user .
# or
uv pip install --user .
```

### If you need to force reinstall

```bash
# With pip
pip install --force-reinstall .

# With uv
uv pip install --reinstall .
```

### If dependencies conflict

```bash
# Install with dependency resolution
pip install --upgrade-strategy eager .

# uv handles this automatically and faster
uv pip install .
```

## ⚡ Performance Comparison

| Operation | pip | uv | Speed Improvement |
|-----------|-----|-----|------------------|
| **Dependency Resolution** | Standard | ⚡ 10-100x faster | Significant |
| **Package Installation** | Standard | ⚡ 2-10x faster | Notable |
| **Cache Management** | Good | ⚡ Excellent | Better |
| **Your Custom Commands** | ✅ Works | ✅ Works identically | Same functionality |

## 📚 Additional Resources

- [Python Packaging User Guide](https://packaging.python.org/)
- [pip documentation](https://pip.pypa.io/)
- [uv documentation](https://docs.astral.sh/uv/)
- [setuptools documentation](https://setuptools.pypa.io/)

---

**Remember:** Always use modern package managers (`pip` or `uv`) instead of `python setup.py install` for reliable, fast, and maintainable Python package management!
